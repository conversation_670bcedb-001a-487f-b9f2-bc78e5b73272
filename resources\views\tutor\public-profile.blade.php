@extends('layouts.app')

@section('title', $profile->public_name . ' - <PERSON><PERSON>')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-12">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tu<PERSON> -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mb-8 transform hover:scale-[1.01] transition-all duration-300 hover:shadow-xl">
            <div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
                <!-- Profile Picture -->
                <div class="relative group">
                    <div class="w-28 h-28 rounded-full overflow-hidden border-4 border-primary/30 shadow-lg transform group-hover:scale-105 transition-all duration-300">
                        <img src="{{ $profile->user->getProfilePictureUrl() }}"
                             alt="{{ $profile->public_name }}"
                             class="w-full h-full object-cover">
                    </div>
                    <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>

                <!-- Tutor Info -->
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:items-center md:space-x-4 mb-3">
                        <h1 class="text-3xl font-bold text-gray-900 hover:text-primary transition-colors duration-300">{{ $profile->public_name }}</h1>
                        @if($profile->user->job_title)
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-primary/10 to-primary/20 text-primary border border-primary/20 hover:from-primary/20 hover:to-primary/30 transition-all duration-300">
                                {{ $profile->user->job_title }}
                            </span>
                        @endif
                    </div>

                    <div class="flex flex-wrap items-center gap-4 mb-4">
                        <p class="text-lg text-gray-600 font-medium">Tutor Ngambiskuy</p>
                        @if($profile->user->location)
                            <div class="flex items-center text-gray-500 hover:text-primary transition-colors duration-300">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $profile->user->location }}
                            </div>
                        @endif
                        @if($profile->user->company)
                            <div class="flex items-center text-gray-500 hover:text-primary transition-colors duration-300">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                {{ $profile->user->company }}
                            </div>
                        @endif
                    </div>

                    @if($profile->description)
                        <p class="text-gray-700 leading-relaxed mb-4 text-base">{{ $profile->description }}</p>
                    @endif

                    <!-- Skills -->
                    @if($profile->user->getSkillsArray())
                        <div class="flex flex-wrap gap-2 mb-4">
                            @foreach($profile->user->getSkillsArray() as $skill)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border border-blue-200 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 transform hover:scale-105">
                                    {{ $skill }}
                                </span>
                            @endforeach
                        </div>
                    @endif

                    <!-- Social Media Links -->
                    @if($profile->user->hasSocialMediaLinks() || $profile->user->website)
                        <div class="flex flex-wrap gap-3 mb-4">
                            @foreach($profile->user->getSocialMediaLinks() as $platform => $url)
                                <a href="{{ $url }}" target="_blank" rel="noopener noreferrer"
                                   class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-300 transform hover:scale-105 border border-gray-200 hover:border-gray-300">
                                    @if($platform === 'linkedin')
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                        LinkedIn
                                    @elseif($platform === 'github')
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                        </svg>
                                        GitHub
                                    @elseif($platform === 'twitter')
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                        </svg>
                                        Twitter
                                    @elseif($platform === 'instagram')
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                        Instagram
                                    @elseif($platform === 'youtube')
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                        </svg>
                                        YouTube
                                    @elseif($platform === 'facebook')
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                        Facebook
                                    @endif
                                </a>
                            @endforeach
                            @if($profile->user->website)
                                <a href="{{ $profile->user->website }}" target="_blank" rel="noopener noreferrer"
                                   class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                    Website
                                </a>
                            @endif
                        </div>
                    @endif

                    <!-- Education Level -->
                    <div class="mt-3">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-300 hover:from-purple-200 hover:to-purple-300 transition-all duration-300 transform hover:scale-105">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                            </svg>
                            {{ $profile->education_level }}
                        </span>
                    </div>
                </div>

                <!-- Contact Button -->
                <div class="flex flex-col space-y-3">
                    <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $profile->phone_number) }}"
                       target="_blank"
                       class="btn btn-primary px-6 py-3 text-center transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700">
                        <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        Hubungi via WhatsApp
                    </a>

                    <p class="text-xs text-gray-500 text-center bg-gray-50 px-3 py-2 rounded-lg">
                        Bergabung sejak {{ $profile->created_at->format('M Y') }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Content Tabs -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                <nav class="-mb-px flex space-x-8 px-8 pt-6" aria-label="Tabs">
                    <button onclick="showTab('courses')" id="courses-tab" class="tab-button active whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            Kursus ({{ $stats['total_courses'] }})
                        </span>
                    </button>
                    <button onclick="showTab('exams')" id="exams-tab" class="tab-button whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Ujian ({{ $stats['total_exams'] }})
                        </span>
                    </button>
                    <button onclick="showTab('blogs')" id="blogs-tab" class="tab-button whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Blog ({{ $stats['total_blog_posts'] }})
                        </span>
                    </button>
                </nav>
            </div>

            <!-- Courses Tab -->
            <div id="courses-content" class="tab-content p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Kursus dari {{ $profile->public_name }}</h2>

                @if($courses->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($courses as $course)
                            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] hover:border-primary/30">
                                <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                                    @if($course->thumbnail)
                                        <img src="{{ asset('storage/' . $course->thumbnail) }}"
                                             alt="{{ $course->title }}"
                                             class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                                    @else
                                        <div class="w-full h-full flex items-center justify-center">
                                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    @endif

                                    <!-- Price Badge -->
                                    <div class="absolute top-3 right-3">
                                        @if($course->is_free)
                                            <span class="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">Gratis</span>
                                        @else
                                            <span class="bg-gradient-to-r from-primary to-orange-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">Rp {{ number_format($course->price, 0, ',', '.') }}</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="p-4">
                                    <div class="flex items-center text-sm text-gray-500 mb-2">
                                        @if($course->category)
                                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">{{ $course->category->name }}</span>
                                        @endif
                                        <span class="ml-auto">{{ $course->level }}</span>
                                    </div>

                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $course->title }}</h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $course->description }}</p>

                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                        <span>{{ $course->total_lessons }} pelajaran</span>
                                        <span>{{ $course->total_duration_minutes }} menit</span>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex text-yellow-400">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($course->average_rating))
                                                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    @else
                                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    @endif
                                                @endfor
                                            </div>
                                            <span class="ml-1 text-sm">{{ number_format($course->average_rating, 1) }}</span>
                                        </div>

                                        <a href="{{ route('course.show', $course) }}" class="text-emerald-600 hover:text-emerald-700 font-medium text-sm">
                                            Lihat Detail →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if($stats['total_courses'] > 6)
                        <div class="text-center mt-8">
                            <a href="{{ route('courses.index') }}?tutor={{ $profile->user->id }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                                Lihat Semua Kursus ({{ $stats['total_courses'] }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Kursus</h3>
                        <p class="text-gray-500">{{ $profile->public_name }} belum membuat kursus. Pantau terus untuk update terbaru!</p>
                    </div>
                @endif
            </div>

            <!-- Exams Tab -->
            <div id="exams-content" class="tab-content p-8 hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Ujian dari {{ $profile->public_name }}</h2>

                @if($exams->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($exams as $exam)
                            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        @if($exam->category)
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">{{ $exam->category->name }}</span>
                                        @endif
                                        <span class="bg-{{ $exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red') }}-100 text-{{ $exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red') }}-800 px-2 py-1 rounded text-xs font-medium">
                                            {{ ucfirst($exam->difficulty_level) }}
                                        </span>
                                    </div>

                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{ $exam->title }}</h3>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $exam->description }}</p>

                                    <div class="space-y-2 mb-4">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ $exam->time_limit }} menit
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            {{ $exam->total_questions }} soal
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Passing score: {{ $exam->passing_score }}%
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="text-lg font-bold text-emerald-600">
                                            @if($exam->price > 0)
                                                Rp {{ number_format($exam->price, 0, ',', '.') }}
                                            @else
                                                Gratis
                                            @endif
                                        </div>

                                        <a href="{{ route('exams.show', $exam) }}" class="text-emerald-600 hover:text-emerald-700 font-medium text-sm">
                                            Lihat Detail →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if($stats['total_exams'] > 6)
                        <div class="text-center mt-8">
                            <a href="{{ route('exams.index') }}?tutor={{ $profile->user->id }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                                Lihat Semua Ujian ({{ $stats['total_exams'] }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                        <p class="text-gray-500">{{ $profile->public_name }} belum membuat ujian. Pantau terus untuk update terbaru!</p>
                    </div>
                @endif
            </div>

            <!-- Blogs Tab -->
            <div id="blogs-content" class="tab-content p-8 hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Blog dari {{ $profile->public_name }}</h2>

                @if($blogPosts->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($blogPosts as $post)
                            <article class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                @if($post->featured_image)
                                    <div class="aspect-video bg-gray-100">
                                        <img src="{{ asset('storage/' . $post->featured_image) }}"
                                             alt="{{ $post->title }}"
                                             class="w-full h-full object-cover">
                                    </div>
                                @endif

                                <div class="p-6">
                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                        @if($post->category)
                                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs mr-3">{{ $post->category->name }}</span>
                                        @endif
                                        <time datetime="{{ $post->published_at->format('Y-m-d') }}">
                                            {{ $post->published_at->format('d M Y') }}
                                        </time>
                                        <span class="mx-2">•</span>
                                        <span>{{ $post->read_time }} menit baca</span>
                                    </div>

                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                                        <a href="{{ route('blog.show', $post) }}" class="hover:text-emerald-600 transition-colors">
                                            {{ $post->title }}
                                        </a>
                                    </h3>

                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $post->excerpt }}</p>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            {{ $post->views_count }} views
                                        </div>

                                        <a href="{{ route('blog.show', $post) }}" class="text-emerald-600 hover:text-emerald-700 font-medium text-sm">
                                            Baca Selengkapnya →
                                        </a>
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>

                    @if($stats['total_blog_posts'] > 6)
                        <div class="text-center mt-8">
                            <a href="{{ route('blog.index') }}?author={{ $profile->user->id }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                                Lihat Semua Blog ({{ $stats['total_blog_posts'] }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Blog</h3>
                        <p class="text-gray-500">{{ $profile->public_name }} belum menulis blog. Pantau terus untuk update terbaru!</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Stats Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:border-primary/30">
                <div class="w-12 h-12 bg-gradient-to-r from-primary/10 to-primary/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-primary mb-2">{{ $stats['total_courses'] }}</div>
                <div class="text-gray-600 font-medium">Kursus Tersedia</div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:border-blue-300">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-blue-600 mb-2">{{ $stats['total_exams'] }}</div>
                <div class="text-gray-600 font-medium">Ujian Tersedia</div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:border-green-300">
                <div class="w-12 h-12 bg-gradient-to-r from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-green-600 mb-2">{{ $stats['total_students'] }}</div>
                <div class="text-gray-600 font-medium">Total Siswa</div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:border-yellow-300">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-yellow-600 mb-2">{{ number_format($stats['average_rating'], 1) }}</div>
                <div class="text-gray-600 font-medium">Rating Rata-rata</div>
            </div>
        </div>

        <!-- About Section -->
        @if($profile->description || $profile->long_description)
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mt-8 transform hover:scale-[1.01] transition-all duration-300 hover:shadow-xl">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-primary/10 to-primary/20 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                Tentang {{ $profile->public_name }}
            </h2>

            @if($profile->long_description)
                <!-- Long Description (Main About Section) -->
                <div class="prose prose-gray max-w-none mb-6">
                    <div class="text-gray-700 leading-relaxed whitespace-pre-line text-base">{{ $profile->long_description }}</div>
                </div>
            @elseif($profile->description)
                <!-- Fallback to Short Description if no long description -->
                <div class="prose prose-gray max-w-none mb-6">
                    <p class="text-gray-700 leading-relaxed text-base">{{ $profile->description }}</p>
                </div>
            @endif

            <!-- Education Info -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <div class="w-6 h-6 bg-gradient-to-r from-purple-100 to-purple-200 rounded-full flex items-center justify-center mr-2">
                        <svg class="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        </svg>
                    </div>
                    Latar Belakang Pendidikan
                </h3>
                <div class="flex items-center space-x-3 bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-lg">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    <span class="text-gray-700 font-medium">{{ $profile->education_level }}</span>
                </div>
            </div>
        </div>
        @endif

        <!-- Back to Home -->
        <div class="text-center mt-8">
            <a href="{{ route('home') }}"
               class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 hover:text-gray-900 font-medium rounded-lg transition-all duration-300 transform hover:scale-105 border border-gray-300 hover:border-gray-400">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Beranda
            </a>
        </div>
    </div>
</div>

<style>
.tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-white/50;
    transition: all 0.3s ease;
}

.tab-button.active {
    @apply border-primary text-primary bg-white shadow-md;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth fade animations for tab content */
.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

/* Pulse animation for stats */
@keyframes pulse-subtle {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

.stats-card:hover {
    animation: pulse-subtle 2s infinite;
}
</style>

<script>
function showTab(tabName) {
    // Hide all tab contents with fade effect
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.style.opacity = '0';
        content.style.transform = 'translateY(10px)';
        setTimeout(() => {
            content.classList.add('hidden');
        }, 150);
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content with fade effect
    setTimeout(() => {
        const selectedContent = document.getElementById(tabName + '-content');
        if (selectedContent) {
            selectedContent.classList.remove('hidden');
            setTimeout(() => {
                selectedContent.style.opacity = '1';
                selectedContent.style.transform = 'translateY(0)';
            }, 50);
        }
    }, 150);

    // Add active class to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
}

// Initialize the first tab as active on page load
document.addEventListener('DOMContentLoaded', function() {
    showTab('courses');

    // Add stats card hover class
    const statsCards = document.querySelectorAll('.grid > div');
    statsCards.forEach(card => {
        card.classList.add('stats-card');
    });
});

// Add smooth scroll behavior for internal links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>

@endsection

@push('scripts')
<script>
// Add tutor context for Nala AI
window.tutorContext = {
    tutor: {
        id: {{ $profile->user_id }},
        name: @json($profile->public_name ?? 'Unknown Tutor'),
        slug: @json($profile->public_name_slug ?? ''),
        description: @json($profile->description ?? ''),
        long_description: @json($profile->long_description ?? ''),
        education_level: @json($profile->education_level ?? ''),
        job_title: @json($profile->user->job_title ?? ''),
        company: @json($profile->user->company ?? ''),
        location: @json($profile->user->location ?? ''),
        skills: @json($profile->user->getSkillsArray() ?? []),
        phone_number: @json($profile->phone_number ?? ''),
        website: @json($profile->user->website ?? ''),
        social_media: @json($profile->user->getSocialMediaLinks() ?? []),
        joined_date: @json($profile->created_at ? $profile->created_at->format('M Y') : 'Unknown'),
        profile_url: @json($profile->getPublicUrlAttribute() ?? '')
    },
    stats: {
        total_courses: {{ $stats['total_courses'] }},
        total_exams: {{ $stats['total_exams'] }},
        total_blog_posts: {{ $stats['total_blog_posts'] }},
        total_students: {{ $stats['total_students'] }},
        average_rating: {{ $stats['average_rating'] }}
    },
    courses: [
        @foreach($courses as $course)
        {
            id: {{ $course->id }},
            title: @json($course->title ?? 'Untitled Course'),
            description: @json($course->description ?? ''),
            price: {{ $course->price ?? 0 }},
            is_free: {{ $course->is_free ? 'true' : 'false' }},
            level: @json($course->level ?? 'Beginner'),
            category: @json($course->category->name ?? 'Uncategorized'),
            total_lessons: {{ $course->total_lessons ?? 0 }},
            total_duration_minutes: {{ $course->total_duration_minutes ?? 0 }},
            average_rating: {{ $course->average_rating ?? 0 }},
            url: @json(route('course.show', $course))
        }@if(!$loop->last),@endif
        @endforeach
    ],
    exams: [
        @foreach($exams as $exam)
        {
            id: {{ $exam->id }},
            title: @json($exam->title ?? 'Untitled Exam'),
            description: @json($exam->description ?? ''),
            price: {{ $exam->price ?? 0 }},
            difficulty_level: @json($exam->difficulty_level ?? 'Beginner'),
            category: @json($exam->category->name ?? 'Uncategorized'),
            time_limit: {{ $exam->time_limit ?? 0 }},
            total_questions: {{ $exam->total_questions ?? 0 }},
            passing_score: {{ $exam->passing_score ?? 0 }},
            url: @json(route('exams.show', $exam))
        }@if(!$loop->last),@endif
        @endforeach
    ],
    blogs: [
        @foreach($blogPosts as $post)
        {
            id: {{ $post->id }},
            title: @json($post->title ?? 'Untitled Post'),
            excerpt: @json($post->excerpt ?? ''),
            category: @json($post->category->name ?? 'Uncategorized'),
            published_at: @json($post->published_at ? $post->published_at->format('d M Y') : 'Unknown'),
            read_time: {{ $post->read_time ?? 0 }},
            views_count: {{ $post->views_count ?? 0 }},
            url: @json(route('blog.show', $post))
        }@if(!$loop->last),@endif
        @endforeach
    ]
};

// Extend Nala config if it exists
if (window.nalaConfig) {
    window.nalaConfig.tutorContext = window.tutorContext;
}
</script>
@endpush
